package com.example.demoziplock

import android.graphics.*

/**
 * Base drawable component class
 */
abstract class DrawableComponent(
    var left: Double,
    var top: Double,
    var width: Double,
    var height: Double
) {
    var alpha: Double = 255.0
    var rotate: Double = 0.0
    var skewX: Double = 0.0
    var skewY: Double = 0.0
    protected val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    protected val children = mutableListOf<DrawableComponent>()
    
    // Touch listeners
    private var onClickDownListener: ((Double, Double) -> Unit)? = null
    private var onTouchMoveListener: ((DrawableComponent, Double, Double) -> Unit)? = null
    private var onUpdateListener: (() -> Unit)? = null

    abstract fun draw(canvas: Canvas)

    fun addChild(child: DrawableComponent) {
        children.add(child)
    }

    fun removeChild(child: DrawableComponent) {
        children.remove(child)
    }

    protected fun drawChildren(canvas: Canvas) {
        children.forEach { it.draw(canvas) }
    }

    fun checkUpdates() {
        onUpdateListener?.invoke()
        children.forEach { it.checkUpdates() }
    }

    fun checkClickDown(x: Double, y: Double): Boolean {
        if (isPointInside(x, y)) {
            onClickDownListener?.invoke(x, y)
            return true
        }
        return children.any { it.checkClickDown(x, y) }
    }

    fun checkTouchMove(x: Double, y: Double) {
        if (isPointInside(x, y)) {
            onTouchMoveListener?.invoke(this, x, y)
        }
        children.forEach { it.checkTouchMove(x, y) }
    }

    fun checkClickUp() {
        children.forEach { it.checkClickUp() }
    }

    private fun isPointInside(x: Double, y: Double): Boolean {
        return x >= left && x <= left + width && y >= top && y <= top + height
    }

    fun setOnClickDownListener(listener: (Double, Double) -> Unit) {
        onClickDownListener = listener
    }

    fun setOnTouchMoveListener(listener: (DrawableComponent, Double, Double) -> Unit) {
        onTouchMoveListener = listener
    }

    fun setOnUpdateListener(listener: () -> Unit) {
        onUpdateListener = listener
    }

    fun getCenterX(): Double = left + width / 2.0
    fun getCenterY(): Double = top + height / 2.0

    fun getRect(): Rect {
        return Rect(left.toInt(), top.toInt(), (left + width).toInt(), (top + height).toInt())
    }
}

/**
 * Container for grouping drawable components
 */
class DrawableContainer(
    left: Double,
    top: Double,
    width: Double,
    height: Double
) : DrawableComponent(left, top, width, height) {

    private var backgroundColor: Int = Color.TRANSPARENT

    fun setBackgroundColor(color: Int) {
        backgroundColor = color
    }

    override fun draw(canvas: Canvas) {
        try {
            val save = canvas.save()
            canvas.rotate(rotate.toFloat(), getCenterX().toFloat(), getCenterY().toFloat())
            canvas.skew(skewX.toFloat(), skewY.toFloat())
            
            paint.alpha = alpha.toInt()
            
            if (backgroundColor != Color.TRANSPARENT) {
                paint.color = backgroundColor
                canvas.drawRect(getRect(), paint)
            }
            
            drawChildren(canvas)
            canvas.restoreToCount(save)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

/**
 * Image drawable component
 */
class DrawableImage(
    left: Double,
    top: Double,
    width: Double,
    height: Double,
    var bitmap: Bitmap?
) : DrawableComponent(left, top, width, height) {

    override fun draw(canvas: Canvas) {
        try {
            val save = canvas.save()
            canvas.rotate(rotate.toFloat(), getCenterX().toFloat(), getCenterY().toFloat())
            canvas.skew(skewX.toFloat(), skewY.toFloat())
            
            paint.alpha = alpha.toInt()
            
            bitmap?.let { bmp ->
                if (!bmp.isRecycled) {
                    val srcRect = Rect(0, 0, bmp.width, bmp.height)
                    val destRect = getRect()
                    canvas.drawBitmap(bmp, srcRect, destRect, paint)
                }
            }
            
            drawChildren(canvas)
            canvas.restoreToCount(save)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

/**
 * Text drawable component
 */
class DrawableText(
    left: Double,
    top: Double,
    width: Double,
    height: Double,
    var text: String
) : DrawableComponent(left, top, width, height) {

    private var textSize: Double = 24.0
    private var textColor: Int = Color.BLACK
    private var textAlign: Paint.Align = Paint.Align.CENTER

    init {
        paint.textAlign = textAlign
    }

    fun setTextSize(size: Double) {
        textSize = size
        paint.textSize = size.toFloat()
    }

    fun setColor(color: Int) {
        textColor = color
        paint.color = color
    }

    fun setTextAlign(align: Paint.Align) {
        textAlign = align
        paint.textAlign = align
    }

    override fun draw(canvas: Canvas) {
        try {
            val save = canvas.save()
            canvas.rotate(rotate.toFloat(), getCenterX().toFloat(), getCenterY().toFloat())
            canvas.skew(skewX.toFloat(), skewY.toFloat())
            
            paint.alpha = alpha.toInt()
            paint.color = textColor
            paint.textSize = textSize.toFloat()
            paint.textAlign = textAlign
            
            val x = when (textAlign) {
                Paint.Align.LEFT -> left.toFloat()
                Paint.Align.RIGHT -> (left + width).toFloat()
                else -> getCenterX().toFloat()
            }
            
            val y = getCenterY().toFloat() + (paint.textSize / 3)
            
            canvas.drawText(text, x, y, paint)
            
            drawChildren(canvas)
            canvas.restoreToCount(save)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

/**
 * Rectangle drawable component
 */
class DrawableRectangle(
    left: Double,
    top: Double,
    width: Double,
    height: Double,
    private var color: Int = Color.GRAY
) : DrawableComponent(left, top, width, height) {

    private var cornerRadius: Float = 0f

    fun setColor(color: Int) {
        this.color = color
    }

    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
    }

    override fun draw(canvas: Canvas) {
        try {
            val save = canvas.save()
            canvas.rotate(rotate.toFloat(), getCenterX().toFloat(), getCenterY().toFloat())
            canvas.skew(skewX.toFloat(), skewY.toFloat())
            
            paint.alpha = alpha.toInt()
            paint.color = color
            paint.style = Paint.Style.FILL
            
            val rect = RectF(left.toFloat(), top.toFloat(), (left + width).toFloat(), (top + height).toFloat())
            
            if (cornerRadius > 0) {
                canvas.drawRoundRect(rect, cornerRadius, cornerRadius, paint)
            } else {
                canvas.drawRect(rect, paint)
            }
            
            drawChildren(canvas)
            canvas.restoreToCount(save)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
