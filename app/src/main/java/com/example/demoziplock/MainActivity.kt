package com.example.demoziplock

import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat

class MainActivity : AppCompatActivity() {

    private lateinit var zipperView: ZipperScreenLock3View

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Create the zipper view programmatically
        zipperView = ZipperScreenLock3View(this)

        // Initialize the zipper lock
        zipperView.initializeZipperLock()

        // Set the view as content
        setContentView(zipperView)

        ViewCompat.setOnApplyWindowInsetsListener(zipperView) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }

    override fun onResume() {
        super.onResume()
        zipperView.resumeRendering()
    }

    override fun onPause() {
        super.onPause()
        zipperView.pauseRendering()
    }

    override fun onDestroy() {
        super.onDestroy()
        zipperView.clearMemory()
    }
}