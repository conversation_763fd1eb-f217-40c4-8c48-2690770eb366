package com.example.demoziplock;

// ZipperView.kt
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.DecelerateInterpolator
import kotlin.math.*

class ZipperScreenLock2View @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // Paint objects
    private val zipperTrackPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#654321")
        strokeWidth = 6f
        style = Paint.Style.STROKE
    }

    private val zipperTeethPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#C0C0C0")
        strokeWidth = 4f
        style = Paint.Style.FILL_AND_STROKE
    }

    private val zipperTeethShadowPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#808080")
        strokeWidth = 4f
        style = Paint.Style.FILL_AND_STROKE
    }

    private val zipperPullerPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#FFD700")
        style = Paint.Style.FILL
        setShadowLayer(6f, 3f, 3f, Color.parseColor("#80000000"))
    }

    private val zipperPullerStrokePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#DAA520")
        style = Paint.Style.STROKE
        strokeWidth = 3f
    }

    private val closedBackgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#2E2E2E")
        style = Paint.Style.FILL
    }

    private val revealedBackgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }

    private val overlayPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#80000000")
        style = Paint.Style.FILL
    }

    // Zipper properties
    private var zipperProgress = 0f // 0f = closed, 1f = fully open
    private var isDragging = false
    private var lastTouchY = 0f

    // Animation
    private var animator: ValueAnimator? = null

    // Dimensions
    private var zipperWidth = 0f
    private var zipperHeight = 0f
    private var teethSpacing = 18f
    private var pullerSize = 50f
    private var maxCurveDistance = 35f

    // Teeth data
    private data class Tooth(
        val originalX: Float,
        val y: Float,
        val leftSide: Boolean
    )

    private val teeth = mutableListOf<Tooth>()

    // Callback
    var onZipperStateChanged: ((progress: Float, isOpening: Boolean) -> Unit)? = null

    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null) // Enable shadow
        setupRevealedBackground()
    }

    private fun setupRevealedBackground() {
        val gradient = RadialGradient(
            0f, 0f, 1000f,
            intArrayOf(
                Color.parseColor("#FF6B6B"),
                Color.parseColor("#4ECDC4"),
                Color.parseColor("#45B7D1"),
                Color.parseColor("#96CEB4"),
                Color.parseColor("#FFEAA7"),
                Color.parseColor("#FD79A8")
            ),
            floatArrayOf(0f, 0.2f, 0.4f, 0.6f, 0.8f, 1f),
            Shader.TileMode.MIRROR
        )
        revealedBackgroundPaint.shader = gradient
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        zipperWidth = w.toFloat()
        zipperHeight = h.toFloat()
        maxCurveDistance = min(zipperWidth * 0.12f, 50f)
        generateTeeth()

        // Update gradient to fit screen
        val gradient = RadialGradient(
            zipperWidth / 2f, zipperHeight / 2f,
            max(zipperWidth, zipperHeight) / 2f,
            intArrayOf(
                Color.parseColor("#FF6B6B"),
                Color.parseColor("#4ECDC4"),
                Color.parseColor("#45B7D1"),
                Color.parseColor("#96CEB4"),
                Color.parseColor("#FFEAA7"),
                Color.parseColor("#FD79A8")
            ),
            floatArrayOf(0f, 0.2f, 0.4f, 0.6f, 0.8f, 1f),
            Shader.TileMode.MIRROR
        )
        revealedBackgroundPaint.shader = gradient
    }

    private fun generateTeeth() {
        teeth.clear()
        val centerX = zipperWidth / 2f
        val trackTop = zipperHeight * 0.1f
        val trackBottom = zipperHeight * 0.9f

        var y = trackTop + teethSpacing
        while (y < trackBottom) {
            // Left side teeth
            teeth.add(Tooth(centerX - 15f, y, true))
            // Right side teeth
            teeth.add(Tooth(centerX + 15f, y, false))
            y += teethSpacing
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        drawBackground(canvas)
        drawRevealedContent(canvas)
        drawZipperOverlay(canvas)
        drawZipperTrack(canvas)
        drawZipperTeeth(canvas)
        drawZipperPuller(canvas)
    }

    private fun drawBackground(canvas: Canvas) {
        // Draw closed background
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), closedBackgroundPaint)
    }

    private fun drawRevealedContent(canvas: Canvas) {
        if (zipperProgress <= 0f) return

        val trackTop = zipperHeight * 0.1f
        val trackBottom = zipperHeight * 0.9f
        val currentOpenHeight = (trackBottom - trackTop) * zipperProgress
        val revealedBottom = trackTop + currentOpenHeight

        // Draw revealed area as full-width rectangle from top to current zipper position
        val revealRect = RectF(
            0f,
            trackTop,
            zipperWidth,
            revealedBottom
        )

        canvas.drawRect(revealRect, revealedBackgroundPaint)

        // Add animated patterns
        drawAnimatedPatterns(canvas, revealRect)

        // Add sparkle effects across the full width
        drawFullWidthSparkles(canvas, revealRect)

        // Add wave effects at the bottom edge
        drawWaveEffect(canvas, revealedBottom)
    }

    private fun drawAnimatedPatterns(canvas: Canvas, rect: RectF) {
        val patternPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.parseColor("#40FFFFFF")
            style = Paint.Style.STROKE
            strokeWidth = 2f
        }

        // Draw animated circles
        val time = System.currentTimeMillis()
        val circleCount = 8

        for (i in 0 until circleCount) {
            val x = rect.width() * (i + 0.5f) / circleCount
            val y = rect.top + rect.height() * 0.3f +
                    sin((time / 1000.0 + i * 0.5) * 2 * PI).toFloat() * 20f
            val radius = 15f + sin((time / 800.0 + i) * PI).toFloat() * 5f

            patternPaint.alpha = (128 * zipperProgress).toInt()
            canvas.drawCircle(x, y, radius, patternPaint)
        }

        // Draw flowing lines
        val linePath = Path()
        val points = 20
        for (i in 0..points) {
            val x = rect.width() * i / points
            val y = rect.top + rect.height() * 0.7f +
                    sin((time / 600.0 + i * 0.3) * 2 * PI).toFloat() * 15f

            if (i == 0) linePath.moveTo(x, y)
            else linePath.lineTo(x, y)
        }

        patternPaint.strokeWidth = 3f
        patternPaint.alpha = (100 * zipperProgress).toInt()
        canvas.drawPath(linePath, patternPaint)
    }

    private fun drawFullWidthSparkles(canvas: Canvas, rect: RectF) {
        if (zipperProgress <= 0f) return

        val sparklePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.WHITE
            style = Paint.Style.FILL
        }

        val sparkleCount = (zipperProgress * 25).toInt()
        val time = System.currentTimeMillis()

        repeat(sparkleCount) { i ->
            val x = (rect.width() * (i * 0.618f) % 1f) // Golden ratio distribution
            val y = rect.top + (rect.height() * (i + 1) / (sparkleCount + 1))
            val size = 2f + (zipperProgress * 4f) +
                    sin((time / 400.0 + i) * PI).toFloat() * 1f
            val alpha = (200 * zipperProgress *
                    (0.5f + 0.5f * sin((time / 300.0 + i * 0.7) * PI))).toInt()

            sparklePaint.alpha = alpha.coerceIn(0, 255)
            drawStar(canvas, x, y, size, sparklePaint)
        }
    }

    private fun drawWaveEffect(canvas: Canvas, bottomY: Float) {
        if (zipperProgress <= 0f) return

        val wavePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.parseColor("#60FFFFFF")
            style = Paint.Style.FILL
        }

        val wavePath = Path()
        val waveHeight = 8f * zipperProgress
        val time = System.currentTimeMillis()

        wavePath.moveTo(0f, bottomY)

        val points = 50
        for (i in 0..points) {
            val x = zipperWidth * i / points
            val waveY = bottomY + sin((time / 500.0 + i * 0.2) * 2 * PI).toFloat() * waveHeight
            wavePath.lineTo(x, waveY)
        }

        wavePath.lineTo(zipperWidth, bottomY + 20f)
        wavePath.lineTo(0f, bottomY + 20f)
        wavePath.close()

        canvas.drawPath(wavePath, wavePaint)
    }

    private fun drawZipperOverlay(canvas: Canvas) {
        if (zipperProgress <= 0f) return

        val trackTop = zipperHeight * 0.1f
        val trackBottom = zipperHeight * 0.9f
        val currentOpenHeight = (trackBottom - trackTop) * zipperProgress

        // Draw semi-transparent overlay on the unopened area
        val unopenedRect = RectF(
            0f,
            trackTop + currentOpenHeight,
            zipperWidth,
            zipperHeight
        )

        overlayPaint.alpha = (50 * (1f - zipperProgress)).toInt()
        canvas.drawRect(unopenedRect, overlayPaint)
    }

    private fun drawZipperTrack(canvas: Canvas) {
        val centerX = zipperWidth / 2f
        val trackTop = zipperHeight * 0.1f
        val trackBottom = zipperHeight * 0.9f

        // Left track - curves out as zipper opens
        val leftTrackPath = Path()
        leftTrackPath.moveTo(centerX - 20f, trackTop)

        val steps = 50
        for (i in 0..steps) {
            val y = trackTop + (trackBottom - trackTop) * i / steps
            val progressAtY = getProgressAtHeight(y)
            val curveDistance = getCurveDistanceAtProgress(progressAtY)
            leftTrackPath.lineTo(centerX - 20f - curveDistance * 0.4f, y)
        }

        canvas.drawPath(leftTrackPath, zipperTrackPaint)

        // Right track - curves out as zipper opens
        val rightTrackPath = Path()
        rightTrackPath.moveTo(centerX + 20f, trackTop)

        for (i in 0..steps) {
            val y = trackTop + (trackBottom - trackTop) * i / steps
            val progressAtY = getProgressAtHeight(y)
            val curveDistance = getCurveDistanceAtProgress(progressAtY)
            rightTrackPath.lineTo(centerX + 20f + curveDistance * 0.4f, y)
        }

        canvas.drawPath(rightTrackPath, zipperTrackPaint)
    }

    private fun drawZipperTeeth(canvas: Canvas) {
        val centerX = zipperWidth / 2f
        val trackTop = zipperHeight * 0.1f
        val trackBottom = zipperHeight * 0.9f
        val currentZipperY = trackTop + (trackBottom - trackTop) * zipperProgress

        for (tooth in teeth) {
            if (tooth.y <= currentZipperY) continue // Don't draw teeth that are "zipped"

            val progressAtY = getProgressAtHeight(tooth.y)
            val curveDistance = getCurveDistanceAtProgress(progressAtY)

            val toothX = if (tooth.leftSide) {
                tooth.originalX - curveDistance
            } else {
                tooth.originalX + curveDistance
            }

            // Draw tooth shadow first
            drawTooth(canvas, toothX + 1f, tooth.y + 1f, tooth.leftSide, zipperTeethShadowPaint)
            // Draw actual tooth
            drawTooth(canvas, toothX, tooth.y, tooth.leftSide, zipperTeethPaint)
        }
    }

    private fun drawTooth(canvas: Canvas, x: Float, y: Float, leftSide: Boolean, paint: Paint) {
        val toothWidth = 12f
        val toothHeight = 8f

        val path = Path()

        if (leftSide) {
            // Left side tooth pointing right
            path.moveTo(x - toothWidth, y - toothHeight/2)
            path.lineTo(x, y)
            path.lineTo(x - toothWidth, y + toothHeight/2)
            path.lineTo(x - toothWidth - 3f, y)
            path.close()
        } else {
            // Right side tooth pointing left
            path.moveTo(x + toothWidth, y - toothHeight/2)
            path.lineTo(x, y)
            path.lineTo(x + toothWidth, y + toothHeight/2)
            path.lineTo(x + toothWidth + 3f, y)
            path.close()
        }

        canvas.drawPath(path, paint)
    }

    private fun getProgressAtHeight(y: Float): Float {
        val trackTop = zipperHeight * 0.1f
        val trackBottom = zipperHeight * 0.9f
        val currentOpenHeight = (trackBottom - trackTop) * zipperProgress
        val relativeY = y - trackTop

        if (relativeY <= 0f) return 1f
        if (relativeY >= currentOpenHeight) return 0f

        return 1f - (relativeY / currentOpenHeight)
    }

    private fun getCurveDistanceAtProgress(progress: Float): Float {
        // Smooth curve that starts from 0 and reaches maximum at full progress
        val smoothProgress = 1f - cos(progress * PI.toFloat() / 2f)
        return maxCurveDistance * smoothProgress * zipperProgress
    }

    private fun drawZipperPuller(canvas: Canvas) {
        val centerX = zipperWidth / 2f
        val trackTop = zipperHeight * 0.1f
        val trackBottom = zipperHeight * 0.9f

        // Calculate puller position
        val currentPullerY = trackTop + (trackBottom - trackTop) * zipperProgress

        // Draw puller shadow
        val shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.parseColor("#60000000")
            style = Paint.Style.FILL
        }

        val shadowRect = RectF(
            centerX - pullerSize / 2f + 4f,
            currentPullerY - pullerSize / 3f + 4f,
            centerX + pullerSize / 2f + 4f,
            currentPullerY + pullerSize / 3f + 4f
        )
        canvas.drawRoundRect(shadowRect, 10f, 10f, shadowPaint)

        // Draw puller body with gradient
        val pullerGradient = LinearGradient(
            centerX - pullerSize / 2f, currentPullerY - pullerSize / 3f,
            centerX + pullerSize / 2f, currentPullerY + pullerSize / 3f,
            intArrayOf(
                Color.parseColor("#FFD700"),
                Color.parseColor("#FFA500"),
                Color.parseColor("#DAA520")
            ),
            floatArrayOf(0f, 0.5f, 1f),
            Shader.TileMode.CLAMP
        )

        val gradientPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            shader = pullerGradient
            style = Paint.Style.FILL
        }

        val pullerRect = RectF(
            centerX - pullerSize / 2f,
            currentPullerY - pullerSize / 3f,
            centerX + pullerSize / 2f,
            currentPullerY + pullerSize / 3f
        )

        canvas.drawRoundRect(pullerRect, 10f, 10f, gradientPaint)
        canvas.drawRoundRect(pullerRect, 10f, 10f, zipperPullerStrokePaint)

        // Draw puller tab
        val tabRect = RectF(
            centerX - pullerSize / 6f,
            currentPullerY - pullerSize / 2f - 10f,
            centerX + pullerSize / 6f,
            currentPullerY - pullerSize / 3f
        )
        canvas.drawRoundRect(tabRect, 6f, 6f, gradientPaint)
        canvas.drawRoundRect(tabRect, 6f, 6f, zipperPullerStrokePaint)

        // Draw multiple highlights for 3D effect
        val highlight1Paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.parseColor("#FFFACD")
            style = Paint.Style.FILL
        }

        val highlight2Paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.parseColor("#80FFFFFF")
            style = Paint.Style.FILL
        }

        canvas.drawCircle(
            centerX - pullerSize / 4f,
            currentPullerY - pullerSize / 6f,
            pullerSize / 12f,
            highlight1Paint
        )

        canvas.drawOval(
            RectF(
                centerX - pullerSize / 3f,
                currentPullerY - pullerSize / 4f,
                centerX + pullerSize / 6f,
                currentPullerY
            ),
            highlight2Paint
        )
    }

    private fun drawStar(canvas: Canvas, x: Float, y: Float, size: Float, paint: Paint) {
        val path = Path()
        val points = 8
        val outerRadius = size
        val innerRadius = size * 0.4f

        for (i in 0 until points) {
            val angle = (i * PI / (points / 2)).toFloat()
            val radius = if (i % 2 == 0) outerRadius else innerRadius
            val px = x + cos(angle) * radius
            val py = y + sin(angle) * radius

            if (i == 0) path.moveTo(px, py)
            else path.lineTo(px, py)
        }
        path.close()
        canvas.drawPath(path, paint)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                val trackTop = zipperHeight * 0.1f
                val trackBottom = zipperHeight * 0.9f
                val pullerY = trackTop + (trackBottom - trackTop) * zipperProgress

                if (abs(event.y - pullerY) < pullerSize &&
                    abs(event.x - zipperWidth / 2f) < pullerSize) {
                    isDragging = true
                    lastTouchY = event.y
                    animator?.cancel()
                    return true
                }
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDragging) {
                    val deltaY = event.y - lastTouchY
                    val trackHeight = zipperHeight * 0.8f

                    val newProgress = zipperProgress + (deltaY / trackHeight)
                    setZipperProgress(newProgress.coerceIn(0f, 1f))

                    lastTouchY = event.y
                    return true
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isDragging) {
                    isDragging = false
                    animateToNearestState()
                    return true
                }
            }
        }
        return super.onTouchEvent(event)
    }

    private fun setZipperProgress(progress: Float) {
        val oldProgress = zipperProgress
        zipperProgress = progress
        onZipperStateChanged?.invoke(zipperProgress, zipperProgress > oldProgress)
        invalidate()
    }

    private fun animateToNearestState() {
        val targetProgress = if (zipperProgress > 0.3f) 1f else 0f

        animator = ValueAnimator.ofFloat(zipperProgress, targetProgress).apply {
            duration = (abs(targetProgress - zipperProgress) * 800).toLong()
            interpolator = DecelerateInterpolator()
            addUpdateListener { animation ->
                setZipperProgress(animation.animatedValue as Float)
            }
            start()
        }
    }

    // Public methods
    fun openZipper(animate: Boolean = true) {
        if (animate) {
            animator?.cancel()
            animator = ValueAnimator.ofFloat(zipperProgress, 1f).apply {
                duration = 1000
                interpolator = DecelerateInterpolator()
                addUpdateListener { animation ->
                    setZipperProgress(animation.animatedValue as Float)
                }
                start()
            }
        } else {
            setZipperProgress(1f)
        }
    }

    fun closeZipper(animate: Boolean = true) {
        if (animate) {
            animator?.cancel()
            animator = ValueAnimator.ofFloat(zipperProgress, 0f).apply {
                duration = 1000
                interpolator = DecelerateInterpolator()
                addUpdateListener { animation ->
                    setZipperProgress(animation.animatedValue as Float)
                }
                start()
            }
        } else {
            setZipperProgress(0f)
        }
    }

    fun getZipperProgress(): Float = zipperProgress

    fun isFullyOpen(): Boolean = zipperProgress >= 1f

    fun isFullyClosed(): Boolean = zipperProgress <= 0f

    // Method to customize revealed content
    fun setRevealedBackground(drawable: Drawable) {
        // Convert drawable to bitmap and create shader
        val bitmap = Bitmap.createBitmap(
            drawable.intrinsicWidth,
            drawable.intrinsicHeight,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        drawable.setBounds(0, 0, canvas.width, canvas.height)
        drawable.draw(canvas)

        val shader = BitmapShader(bitmap, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
        revealedBackgroundPaint.shader = shader
        invalidate()
    }

    fun setRevealedBackgroundColor(color: Int) {
        revealedBackgroundPaint.shader = null
        revealedBackgroundPaint.color = color
        invalidate()
    }
}
