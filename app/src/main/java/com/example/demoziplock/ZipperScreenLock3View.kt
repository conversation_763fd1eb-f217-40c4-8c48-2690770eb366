package com.example.demoziplock

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.SurfaceHolder
import android.view.SurfaceView
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.*

/**
 * ZipperScreenLock3View - A zipper screen lock view based on ZipperLockView architecture
 * Uses SurfaceView for smooth rendering and component-based drawing system
 */
class ZipperScreenLock3View : SurfaceView, Runnable {

    companion object {
        private const val TAG = "ZipperScreenLock3View"
        
        // Shared canvas for drawing operations
        @JvmStatic
        var sharedCanvas: Canvas? = null
    }

    /**
     * Interface for handling drawing operations
     */
    interface DrawingListener {
        fun onDraw(canvas: Canvas)
    }

    // === RENDERING STATE ===
    private var isRenderingFinished = false
    private var hasDrawnOnce = false
    private var isCurrentlyRunning = false
    private var isPausedState = false
    private var isInitialized = false
    private var isClosing = false

    // === THREAD AND SURFACE ===
    private val surfaceHolder: SurfaceHolder = holder
    private var renderingThread: Thread? = null
    private val drawingListeners = mutableListOf<DrawingListener>()

    // === SCREEN DIMENSIONS ===
    private var screenWidth = 0.0
    private var screenHeight = 0.0

    // === DRAWING COMPONENTS ===
    private var mainScene: DrawableContainer? = null
    private var backgroundContainer: DrawableContainer? = null

    // === BACKGROUND ===
    private var backgroundImage: DrawableImage? = null
    private var backgroundBitmap: Bitmap? = null

    // === ZIPPER COMPONENTS ===
    private var zipperImage: DrawableImage? = null
    private var zipperClickedDown = false
    private var zipperLastY = 0.0
    private var zipperProgress = 0.0 // 0.0 to 1.0

    // === ZIPPER TEETH/ROWS ===
    private var leftRowImages: MutableList<DrawableImage>? = null
    private var rightRowImages: MutableList<DrawableImage>? = null

    // === TIME DISPLAY ===
    private var timeContainer: DrawableContainer? = null
    private var hoursMinutesLabel: DrawableText? = null
    private var secondsLabel: DrawableText? = null
    private var dateLabel: DrawableText? = null

    // === TOUCH HANDLING ===
    private var lastTouchX = 0.0
    private var lastTouchY = 0.0

    // === PAINTS ===
    private val defaultPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.WHITE
        textAlign = Paint.Align.CENTER
    }

    /**
     * Constructors
     */
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        initializeView()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initializeView()
    }

    constructor(context: Context) : super(context) {
        initializeView()
    }

    /**
     * Initialize the view
     */
    private fun initializeView() {
        surfaceHolder.setFormat(PixelFormat.TRANSLUCENT)
        isDrawingCacheEnabled = true
        setupTouchHandling()
    }

    /**
     * Initialize all zipper lock components
     */
    fun initializeZipperLock(): Boolean {
        try {
            if (isInitialized) return true

            isPausedState = true
            isClosing = false
            isInitialized = true

            // Initialize screen dimensions
            initializeScreen()

            // Create demo background
            createDemoBackground()

            // Setup main scene
            setupMainScene()

            // Initialize all layers
            initializeBackgroundLayer()
            initializeZipperLayer()
            initializeActionLayer()

            // Add main drawing listener
            addDrawingListener(object : DrawingListener {
                override fun onDraw(canvas: Canvas) {
                    performMainDrawing(canvas)
                }
            })

            Log.d(TAG, "ZipperLock3 initialized successfully")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize ZipperLock3", e)
            return false
        }
    }

    /**
     * Initialize screen dimensions
     */
    private fun initializeScreen() {
        screenWidth = width.toDouble()
        screenHeight = height.toDouble()
        
        if (screenWidth <= 0 || screenHeight <= 0) {
            // Use default values if not measured yet
            screenWidth = 1080.0
            screenHeight = 1920.0
        }
    }

    /**
     * Create demo background bitmap
     */
    private fun createDemoBackground() {
        if (screenWidth > 0 && screenHeight > 0) {
            backgroundBitmap = Bitmap.createBitmap(
                screenWidth.toInt(),
                screenHeight.toInt(),
                Bitmap.Config.ARGB_8888
            )
            val canvas = Canvas(backgroundBitmap!!)
            
            // Create gradient background
            val gradient = LinearGradient(
                0f, 0f, screenWidth.toFloat(), screenHeight.toFloat(),
                intArrayOf(Color.parseColor("#1a1a2e"), Color.parseColor("#16213e"), Color.parseColor("#0f3460")),
                null,
                Shader.TileMode.CLAMP
            )
            val gradientPaint = Paint().apply { shader = gradient }
            canvas.drawRect(0f, 0f, screenWidth.toFloat(), screenHeight.toFloat(), gradientPaint)
            
            // Add some decorative elements
            val decorPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                color = Color.parseColor("#533483")
                alpha = 100
            }
            
            // Draw some circles
            for (i in 0..5) {
                val x = (screenWidth * Math.random()).toFloat()
                val y = (screenHeight * Math.random()).toFloat()
                val radius = (50 + Math.random() * 100).toFloat()
                canvas.drawCircle(x, y, radius, decorPaint)
            }
        }
    }

    /**
     * Setup main scene container
     */
    private fun setupMainScene() {
        backgroundContainer = DrawableContainer(0.0, 0.0, screenWidth, screenHeight)
        mainScene = DrawableContainer(0.0, 0.0, screenWidth, screenHeight)
        mainScene?.addChild(backgroundContainer)
    }

    /**
     * Initialize background layer
     */
    private fun initializeBackgroundLayer() {
        backgroundImage = DrawableImage(0.0, 0.0, screenWidth, screenHeight, backgroundBitmap)
        backgroundContainer?.addChild(backgroundImage ?: return)
    }

    /**
     * Initialize zipper layer
     */
    private fun initializeZipperLayer() {
        // Create zipper bitmap
        val zipperBitmap = createZipperBitmap()
        zipperImage = DrawableImage(0.0, 0.0, screenWidth / 5.0, screenWidth / 2.4, zipperBitmap)
        mainScene?.addChild(zipperImage ?: return)
        zipperImage?.left = screenWidth / 2.0 - (zipperImage?.width ?: return) / 2.0

        // Setup zipper click listener
        zipperImage?.setOnClickDownListener { x, y ->
            zipperClickedDown = true
        }

        // Setup zipper move listener
        zipperImage?.setOnTouchMoveListener { _, x, y ->
            handleZipperMove(x, y)
        }
    }

    /**
     * Create zipper bitmap programmatically
     */
    private fun createZipperBitmap(): Bitmap {
        val width = (screenWidth / 5.0).toInt()
        val height = (screenWidth / 2.4).toInt()
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // Draw zipper pull
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.color = Color.LTGRAY
        canvas.drawRoundRect(
            RectF(width * 0.2f, 0f, width * 0.8f, height * 0.3f),
            8f, 8f, paint
        )
        
        // Draw zipper handle
        paint.color = Color.GRAY
        canvas.drawRoundRect(
            RectF(width * 0.4f, height * 0.25f, width * 0.6f, height * 0.4f),
            4f, 4f, paint
        )
        
        return bitmap
    }

    /**
     * Initialize action layer (time, rows)
     */
    private fun initializeActionLayer() {
        initializeTimeDisplay()
        initializeRowAnimations()
    }
