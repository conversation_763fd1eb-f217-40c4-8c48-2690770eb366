/*
package com.example.demoziplock.zipper

import android.graphics.Bitmap
import android.graphics.Canvas
import android.util.Log
import androidx.core.graphics.withRotation

class ZipperImagePart : ZipperImage {
    var imageRect: ZipperRect? = null
    var logedImageRecycled = false

    */
/* JADX INFO: super call moved to the top of the method (can break code semantics) *//*

    constructor(
        d: Double,
        d2: Double,
        d3: Double,
        d4: Double,
        zipperRect: ZipperRect?,
        bitmap: Bitmap?
    ) : super(d, d2, d3, d4, bitmap) {
        imageRect = zipperRect
    }

    override fun Draw(canvas: Canvas) {
        canvas.withRotation(
            getRotate().toInt().toFloat(), GetCenterX().toInt().toFloat(), centerY.toInt().toFloat()
        ) {
            paint.alpha = getAlpha().toInt()
            canvas.skew(skewX.toInt().toFloat(), skewY.toInt().toFloat())
            if (!image!!.isRecycled || logedImageRecycled) {
                canvas.drawBitmap(image ?: return, imageRect?.GetRect(), GetRect(), paint)
            } else {
                logedImageRecycled = true
                Log.i("img recycled UimagePart", "image recycled UimagePart")
            }
        }
        drawChildrens(canvas)
    }


}*/
