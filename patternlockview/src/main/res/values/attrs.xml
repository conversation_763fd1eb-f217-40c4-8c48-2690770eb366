<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="PatternLockView">
        <attr name="dotCount" format="integer"/>
        <attr name="dotNormalSize" format="dimension"/>
        <attr name="dotSelectedSize" format="dimension"/>
        <attr name="pathWidth" format="dimension"/>
        <attr name="aspectRatioEnabled" format="boolean"/>
        <attr name="aspectRatio" format="enum">
            <enum name="square" value="0"/>
            <enum name="width_bias" value="1"/>
            <enum name="height_bias" value="2"/>
        </attr>
        <attr name="normalStateColor" format="color"/>
        <attr name="correctStateColor" format="color"/>
        <attr name="wrongStateColor" format="color"/>
        <attr name="dotAnimationDuration" format="integer"/>
        <attr name="pathEndAnimationDuration" format="integer"/>
    </declare-styleable>
</resources>