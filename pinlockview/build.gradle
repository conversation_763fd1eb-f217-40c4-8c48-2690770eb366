apply plugin: 'com.android.library'

android {
    namespace 'com.andrognito.pinlockview'
    compileSdkVersion 35
    //buildToolsVersion "25.0.3"

    defaultConfig {
        minSdkVersion 24
        targetSdkVersion 35
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    //compile fileTree(dir: 'libs', include: ['*.jar'])
    //testCompile 'junit:junit:4.12'
   // compile 'com.android.support:appcompat-v7:25.3.1'
    //compile 'com.android.support:recyclerview-v7:25.3.1'
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.activity)
    implementation(libs.androidx.constraintlayout)
}
