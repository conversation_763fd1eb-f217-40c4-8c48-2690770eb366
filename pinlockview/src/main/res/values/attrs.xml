<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="PinLockView">
        <attr name="pinLength" format="integer" />
        <attr name="keypadTextColor" format="color" />
        <attr name="keypadTextSize" format="dimension" />
        <attr name="keypadButtonSize" format="dimension" />
        <attr name="keypadVerticalSpacing" format="dimension" />
        <attr name="keypadHorizontalSpacing" format="dimension" />
        <attr name="keypadButtonBackgroundDrawable" format="integer" />
        <attr name="keypadDeleteButtonDrawable" format="integer" />
        <attr name="keypadDeleteButtonSize" format="dimension" />
        <attr name="keypadShowDeleteButton" format="boolean" />
        <attr name="keypadDeleteButtonPressedColor" format="color" />

        <attr name="dotEmptyBackground" format="reference" />
        <attr name="dotFilledBackground" format="reference" />
        <attr name="dotDiameter" format="dimension" />
        <attr name="dotSpacing" format="dimension" />
        <attr name="indicatorType" format="enum">
            <enum name="fixed" value="0" />
            <enum name="fill" value="1" />
            <enum name="fillWithAnimation" value="2" />
        </attr>

    </declare-styleable>

</resources>